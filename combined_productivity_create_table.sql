-- Combined Sales, Cases, Stock and SRD table (nsrp, srp pallet etc. info)
-- This combines data from item_sold, cases, stock, item_sold_dotcom and SRD data into one comprehensive table

-- Calendar setup (common for all)
UNCACHE TABLE IF EXISTS cal;
DROP VIEW IF EXISTS cal;
CACHE TABLE cal AS
SELECT dmtm_d_code, dtdw_day_desc_en FROM dm.dim_time_d WHERE dmtm_fw_code BETWEEN 'f2025w24'  AND 'f2025w24';

-- Hierarchy setup (common for all)
UNCACHE TABLE IF EXISTS hier;
DROP VIEW IF EXISTS hier;
CACHE TABLE hier AS
SELECT
	LPAD(div_code,4,"0") AS div_code,
	LPAD(dep_code,4,"0") AS dep_code,
	LPAD(sec_code,4,"0") AS sec_code,
	LPAD(grp_code,4,"0") AS grp_code,
	LPAD(sgr_code,4,"0") AS sgr_code,
	pmg
FROM tesco_analysts.hierarchy_spm
WHERE SUBSTRING(pmg, 1, 3) IN ('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP');

-- Sales data (item_sold - base)
UNCACHE TABLE IF EXISTS sal_sms;
DROP VIEW IF EXISTS sal_sms;
CACHE TABLE sal_sms AS
SELECT
	sunit.slsms_cntr_id,
	sunit.slsms_dmat_id,
	sunit.slsms_dmst_id,
	cal.dtdw_day_desc_en,
	SUM(sunit.slsms_unit) AS slsms_unit,
	SUM(sunit.slsms_salex) AS slsms_salex
FROM
	cal AS cal
JOIN
	dw.sl_sms sunit
	ON
		cal.dmtm_d_code = sunit.part_col AND
		sunit.slsms_cntr_id IN (1,2,4)
GROUP BY
	sunit.slsms_cntr_id,
	sunit.slsms_dmat_id,
	sunit.slsms_dmst_id,
	cal.dtdw_day_desc_en;

-- Cases data
UNCACHE TABLE IF EXISTS sal_cases;
DROP VIEW IF EXISTS sal_cases;
CACHE TABLE sal_cases AS
SELECT
	cases.store,
	cases.int_cntr_id,
	cases.product,
	cal.dtdw_day_desc_en,
	SUM(cases.qty) AS cases_unit
FROM
	cal AS cal
JOIN
	stg_go.go_106_order_receiving cases
	ON
		cal.dmtm_d_code = DATE_FORMAT(DATE_SUB(FROM_UNIXTIME(UNIX_TIMESTAMP(CAST(cases.part_col AS STRING), 'yyyyMMdd')), 1), 'yyyyMMdd') AND
		cases.int_cntr_id IN (1,2,4)
GROUP BY
	cases.store,
	cases.int_cntr_id,
	cases.product,
	cal.dtdw_day_desc_en;

-- Stock data
UNCACHE TABLE IF EXISTS sal_stock;
DROP VIEW IF EXISTS sal_stock;
CACHE TABLE sal_stock AS
SELECT
	stock.slstks_cntr_id,
	stock.slstks_dmat_id,
	stock.slstks_dmst_id,
	cal.dtdw_day_desc_en,
	SUM(stock.slstks_stock_unit_sl) AS stock_unit,
	AVG(stock.slstks_price) AS slstks_price
FROM
	cal AS cal
JOIN
	dw.sl_stocks stock
	ON
		cal.dmtm_d_code = stock.part_col AND
		stock.slstks_cntr_id IN (1,2,4)
GROUP BY
	stock.slstks_cntr_id,
	stock.slstks_dmat_id,
	stock.slstks_dmst_id,
	cal.dtdw_day_desc_en;

-- Dotcom sales data
UNCACHE TABLE IF EXISTS sal_dotcom;
DROP VIEW IF EXISTS sal_dotcom;
CACHE TABLE sal_dotcom AS
SELECT
	sunit.sltrg_cntr_id,
	sunit.sltrg_dmst_id,
	sunit.sltrg_dmat_id,
	cal.dtdw_day_desc_en,
	SUM(sunit.sltrg_tr_unit) AS slsms_unit_dotcom
FROM
	cal AS cal
JOIN
	dw.sl_trg sunit
	ON
		cal.dmtm_d_code = sunit.part_col AND
		sunit.sltrg_cntr_id IN (1,2,4)
GROUP BY
	sunit.sltrg_cntr_id,
	sunit.sltrg_dmst_id,
	sunit.sltrg_dmat_id,
	cal.dtdw_day_desc_en;

-- Get unique store/product combinations that have sales
UNCACHE TABLE IF EXISTS store_product_combinations;
DROP VIEW IF EXISTS store_product_combinations;
CACHE TABLE store_product_combinations AS
SELECT DISTINCT
	stores.cntr_id,
	stores.cntr_code,
	stores.dmst_store_id,
	stores.dmst_store_code,
	mstr.slad_dmat_id,
	mstr.slad_tpnb,
	hier.pmg
FROM
	sal_sms AS sunit
JOIN
	dm.dim_artgld_details mstr
	ON
		mstr.slad_dmat_id = sunit.slsms_dmat_id AND
		mstr.cntr_id = sunit.slsms_cntr_id AND
		mstr.cntr_id IN (1,2,4)
JOIN
	hier
	ON
		mstr.dmat_div_code = hier.div_code AND
		mstr.dmat_dep_code = hier.dep_code AND
		mstr.dmat_sec_code = hier.sec_code AND
		mstr.dmat_grp_code = hier.grp_code AND
		mstr.dmat_sgr_code = hier.sgr_code
JOIN
	dm.dim_stores stores
	ON
		stores.cntr_id = sunit.slsms_cntr_id AND
		stores.dmst_store_id = sunit.slsms_dmst_id AND
		stores.cntr_id IN (1,2,4) AND
		stores.convenience IN ('Convenience', 'HM')
WHERE
	sunit.slsms_unit > 0 AND
	sunit.slsms_salex > 0;

-- Create base dimension table with 7 days for each store/product combination that has sales
UNCACHE TABLE IF EXISTS base_dimensions;
DROP VIEW IF EXISTS base_dimensions;
CACHE TABLE base_dimensions AS
SELECT
	sp.cntr_id,
	sp.cntr_code,
	sp.dmst_store_id,
	sp.dmst_store_code,
	cal.dtdw_day_desc_en,
	sp.slad_dmat_id,
	sp.slad_tpnb,
	sp.pmg
FROM
	store_product_combinations sp
CROSS JOIN
	cal
GROUP BY 
  sp.cntr_id,
	sp.cntr_code,
	sp.dmst_store_id,
	sp.dmst_store_code,
	cal.dtdw_day_desc_en,
	sp.slad_dmat_id,
	sp.slad_tpnb,
	sp.pmg;

-- Part 1: Create the CTE as a temporary view
CREATE OR REPLACE TEMPORARY VIEW productivity_metrics AS
WITH srd_base_data AS (
    SELECT 
        Country,
        Store_Number,
        Product_id,
        Position_Capacity,
        Planogram_on_Store,
        CaseTotalNumber,
        TrayTotalNumber,
        Pallet_info,
        merchstyle_string,
        Division,
        Department,
        Section,
        Group as Group_,
        Subgroup,
        Displaygroup_description,
        Displaygroup,
        -- Create store codes based on country
        CASE 
            WHEN Country = 'HU' THEN CONCAT('4', CAST(Store_Number AS STRING))
            WHEN Country = 'SK' THEN CONCAT('2', CAST(Store_Number AS STRING))
            WHEN Country = 'CZ' THEN CONCAT('1', CAST(Store_Number AS STRING))
            ELSE '0'
        END AS new_store_number,
        
        -- Set Planogram_on_Store to 1 if it's 0
        CASE WHEN Planogram_on_Store = 0 THEN 1 ELSE Planogram_on_Store END AS planogram_adjusted,
        
        -- Calculate icase
        CASE WHEN CaseTotalNumber = 0 THEN TrayTotalNumber ELSE CaseTotalNumber END AS icase
    FROM sch_analysts.tbl_ce_ckb_Live_All_location_StorePogProduct
),

capacity_calc AS (
    SELECT 
        Country,
        new_store_number,
        Product_id,
        icase,
        Pallet_info,
        merchstyle_string,
        Division,
        Department,
        Section,
        Group_,
        Subgroup,
        Displaygroup_description,
        Displaygroup,
        Position_Capacity * planogram_adjusted AS Position_Capacity_X_Planogram_on_Store
    FROM srd_base_data
),

shelf_ratio AS (
    SELECT 
        *,
        Position_Capacity_X_Planogram_on_Store / 
        SUM(Position_Capacity_X_Planogram_on_Store) OVER (
            PARTITION BY new_store_number, Product_id
        ) AS shelf_cap_ratio
    FROM capacity_calc
),

categories_with_ice_cream AS (
    SELECT 
        Country,
        new_store_number,
        Product_id,
        Position_Capacity_X_Planogram_on_Store,
        icase,
        Displaygroup_description,
        Displaygroup,
        shelf_cap_ratio,
        
        -- Handle Division, Department, Section nulls and blanks
        CASE WHEN Division = '' OR Division IS NULL THEN 0 ELSE CAST(Division AS INT) END AS division_clean,
        CASE WHEN Department = '' OR Department IS NULL THEN 0 ELSE CAST(Department AS INT) END AS department_clean,
        CASE WHEN Section = '' OR Section IS NULL THEN 0 ELSE CAST(Section AS INT) END AS section_clean,
        
        -- Calculate pallet categories
        CASE WHEN Pallet_info = 'Pallet' THEN shelf_cap_ratio ELSE 0 END AS full_pallet,
        CASE WHEN Pallet_info = 'Half_Pallet' THEN shelf_cap_ratio ELSE 0 END AS mu,
        CASE WHEN Pallet_info = 'Split_Pallet' THEN shelf_cap_ratio ELSE 0 END AS split_pallet,
        
        -- Calculate base NSRP and SRP
        CASE 
            WHEN merchstyle_string IN ('Unit', 'Display', 'Alternate', 'Loose', 'Log Stack') 
                 AND Pallet_info NOT IN ('Half_Pallet', 'Pallet', 'Split_Pallet')
            THEN shelf_cap_ratio 
            ELSE 0 
        END AS nsrp_base,
        
        CASE 
            WHEN merchstyle_string IN ('Tray', 'Case') 
                 AND Pallet_info NOT IN ('Half_Pallet', 'Pallet', 'Split_Pallet')
            THEN shelf_cap_ratio 
            ELSE 0 
        END AS srp_base,
        
        -- Ice cream NSRP special case
        CASE 
            WHEN CASE WHEN Division = '' OR Division IS NULL THEN 0 ELSE CAST(Division AS INT) END = 1 
                 AND CASE WHEN Department = '' OR Department IS NULL THEN 0 ELSE CAST(Department AS INT) END = 17 
                 AND CASE WHEN Section = '' OR Section IS NULL THEN 0 ELSE CAST(Section AS INT) END = 1702
                 AND merchstyle_string IN ('Loose', 'Alternate')
            THEN shelf_cap_ratio 
            ELSE 0 
        END AS icream_nsrp
    FROM shelf_ratio
),

final_categories AS (
    SELECT 
        Country,
        new_store_number,
        Product_id,
        Position_Capacity_X_Planogram_on_Store,
        icase,
        Displaygroup_description,
        Displaygroup,
        
        -- Apply ice cream adjustment: if icream_nsrp > 0, set other categories to 0
        CASE WHEN icream_nsrp > 0 THEN 0 ELSE srp_base END AS srp,
        CASE WHEN icream_nsrp > 0 THEN 0 ELSE nsrp_base END AS nsrp,
        CASE WHEN icream_nsrp > 0 THEN 0 ELSE mu END AS mu,
        CASE WHEN icream_nsrp > 0 THEN 0 ELSE full_pallet END AS full_pallet,
        CASE WHEN icream_nsrp > 0 THEN 0 ELSE split_pallet END AS split_pallet,
        icream_nsrp,
        
        -- Create flags
        CASE WHEN LOWER(Displaygroup_description) LIKE '%checkout%' THEN 1 ELSE 0 END AS checkout_stand_flag,
        CASE WHEN LOWER(Displaygroup_description) LIKE '%clipstrip%' THEN 1 ELSE 0 END AS clipstrip_flag,
        CASE WHEN Displaygroup IN ('Z2D', 'L1N') THEN 1 ELSE 0 END AS backroom_flag
    FROM categories_with_ice_cream
)

-- Final aggregated productivity metrics
SELECT 
    Country as country,
    new_store_number as store,
    Product_id as tpnb,
    SUM(srp) as srp,
    SUM(nsrp) as nsrp,
    SUM(mu) as mu,
    SUM(full_pallet) as full_pallet,
    SUM(split_pallet) as split_pallet,
    SUM(icream_nsrp) as icream_nsrp,
    SUM(Position_Capacity_X_Planogram_on_Store) as shelfCapacity,
    AVG(icase) as icase,
    MAX(checkout_stand_flag) as checkout_stand_flag,
    MAX(clipstrip_flag) as clipstrip_flag,
    MAX(backroom_flag) as backroom_flag
FROM final_categories
GROUP BY 
    Country,
    new_store_number,
    Product_id;

-- Part 2: Create the external table using the temporary view
DROP TABLE IF EXISTS sch_analysts.tbl_ce_sales_cases_stock_productivity_phrubos;

CREATE EXTERNAL TABLE sch_analysts.tbl_ce_sales_cases_stock_productivity_phrubos
TBLPROPERTIES('external.table.purge'='TRUE')
STORED AS ORC
LOCATION "s3a://cep-sch-analysts-db/sch_analysts_external/tbl_ce_sales_cases_stock_productivity_phrubos" AS
SELECT
	-- Common dimensions
	base.cntr_code AS country,
	cast(base.dmst_store_code as INT) AS store,
	base.dtdw_day_desc_en as day,
	base.pmg AS pmg,
	cast(base.slad_tpnb as INT) AS tpnb,
	
	-- Sales data from item_sold (base)
	COALESCE(sunit.slsms_unit/1, 0) AS sold_units,
	COALESCE(sunit.slsms_salex/1, 0) AS sales_excl_vat,
	
	-- Cases data
	COALESCE(cases.cases_unit/1, 0) AS unit,
	
	-- Stock data  
	COALESCE(stock.stock_unit/1, 0) AS stock,
	COALESCE(stock.slstks_price, 0) AS item_price,
	
	-- Dotcom sales data
	COALESCE(dotcom.slsms_unit_dotcom/1, 0) AS sold_units_dotcom,
	
	-- Productivity metrics from the temporary view
	COALESCE(metrics.srp, 0) AS srp,
	COALESCE(metrics.nsrp, 0) AS nsrp,
	COALESCE(metrics.mu, 0) AS mu,
	COALESCE(metrics.full_pallet, 0) AS full_pallet,
	COALESCE(metrics.split_pallet, 0) AS split_pallet,
	COALESCE(metrics.icream_nsrp, 0) AS icream_nsrp,
	COALESCE(metrics.shelfCapacity, 0) AS shelfCapacity,
	COALESCE(metrics.icase, 0) AS icase,
	COALESCE(metrics.checkout_stand_flag, 0) AS checkout_stand_flag,
	COALESCE(metrics.clipstrip_flag, 0) AS clipstrip_flag,
	COALESCE(metrics.backroom_flag, 0) AS backroom_flag

FROM
	base_dimensions AS base
	
-- LEFT JOIN for sales data
LEFT JOIN
	sal_sms AS sunit
	ON
		sunit.slsms_cntr_id = base.cntr_id AND
		sunit.slsms_dmat_id = base.slad_dmat_id AND
		sunit.slsms_dmst_id = base.dmst_store_id AND
		sunit.dtdw_day_desc_en = base.dtdw_day_desc_en
		
-- LEFT JOIN for cases data (matching on tpnb and store)
LEFT JOIN
	sal_cases AS cases
	ON
		cases.int_cntr_id = base.cntr_id AND
		CAST(cases.product AS STRING) = CAST(base.slad_tpnb AS STRING) AND
		CONCAT(CAST(cases.int_cntr_id AS STRING), cases.store) = CAST(base.dmst_store_code AS STRING) AND
		cases.dtdw_day_desc_en = base.dtdw_day_desc_en
		
-- LEFT JOIN for stock data
LEFT JOIN
	sal_stock AS stock
	ON
		stock.slstks_cntr_id = base.cntr_id AND
		stock.slstks_dmat_id = base.slad_dmat_id AND
		stock.slstks_dmst_id = base.dmst_store_id AND
		stock.dtdw_day_desc_en = base.dtdw_day_desc_en
		
-- LEFT JOIN for dotcom data
LEFT JOIN
	sal_dotcom AS dotcom
	ON
		dotcom.sltrg_cntr_id = base.cntr_id AND
		dotcom.sltrg_dmat_id = base.slad_dmat_id AND
		dotcom.sltrg_dmst_id = base.dmst_store_id AND
		dotcom.dtdw_day_desc_en = base.dtdw_day_desc_en

-- LEFT JOIN for productivity data (matching on store+tpnb combination)
LEFT JOIN
	productivity_metrics AS metrics
	ON
		CAST(base.dmst_store_code AS STRING) = CAST(metrics.store AS STRING) AND
		CAST(base.slad_tpnb AS STRING) = CAST(metrics.tpnb AS STRING)

ORDER BY
	base.dmst_store_code,
	base.pmg,
	base.slad_tpnb,
	base.dtdw_day_desc_en;